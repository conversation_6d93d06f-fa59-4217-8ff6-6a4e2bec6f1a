# 草地控制系统使用说明

## 概述
这个系统为 CS_MowGrass 脚本添加了控制部分草消失和获取消失数量的功能。

## 主要功能
1. **控制草的消失**: 可以让指定区域内的草消失
2. **获取消失数量**: 实时获取已消失草的数量
3. **恢复草地**: 可以恢复所有已消失的草
4. **交互式控制**: 支持鼠标点击和键盘控制

## 文件说明

### 核心文件
- `CS_MowGrass.cs` - 主要的草地渲染脚本（已修改）
- `CS_MowGrass_URP.shader` - URP着色器（已修改）
- `GrassController.cs` - 草地控制器脚本（新增）
- `GrassControlUI.cs` - UI控制脚本（新增）

### 主要修改
1. **CS_MowGrass.cs**:
   - 扩展了 `SourceVertex` 结构体，添加了 `grassState` 字段
   - 添加了草地状态管理相关字段和方法
   - 实现了草的消失、恢复和计数功能

2. **CS_MowGrass_URP.shader**:
   - 更新了 `DrawVertex` 结构体以支持草的状态
   - 修改了顶点着色器以处理消失的草
   - 同时更新了主渲染Pass和ShadowCaster Pass

## 使用方法

### 基本设置
1. 确保场景中有 CS_MowGrass 组件
2. 添加 GrassController 脚本到场景中的任意GameObject
3. 在 GrassController 中设置对 CS_MowGrass 的引用
4. （可选）添加 GrassControlUI 脚本来创建UI界面

### 控制方式

#### 鼠标控制
- **左键点击**: 在点击位置让指定半径内的草消失

#### 键盘控制
- **R键**: 恢复所有已消失的草
- **空格键**: 在随机位置让草消失

#### 代码控制
```csharp
// 获取草地控制器引用
CS_MowGrass grassScript = GetComponent<CS_MowGrass>();

// 让指定索引的草消失
grassScript.MakeGrassDisappear(grassIndex);

// 让指定区域内的草消失
grassScript.MakeGrassDisappearInArea(position, radius);

// 恢复所有草
grassScript.RestoreAllGrass();

// 获取消失的草数量
int count = grassScript.GetDisappearedGrassCount();
```

### UI功能
如果使用了 GrassControlUI 脚本，会自动创建包含以下元素的UI：
- 消失草数量显示
- 操作说明文本
- 消失半径滑块
- 恢复按钮
- 随机消失按钮

## 参数说明

### GrassController 参数
- `disappearRadius`: 草消失的半径范围
- `groundLayerMask`: 地面检测的层级遮罩
- `restoreKey`: 恢复所有草的按键
- `randomDisappearKey`: 随机消失草的按键

### CS_MowGrass 新增字段
- `m_DisappearedGrassCount`: 已消失草的数量
- `m_GrassStates`: 每根草的状态列表
- `m_VerticesData`: 顶点数据列表

## 性能考虑
1. 每次更新草的状态时会重新上传顶点数据到GPU
2. 对于大量草地，建议批量处理状态更新
3. 消失的草通过在着色器中移到视野外来实现，不会减少渲染调用

## 扩展建议
1. 添加草消失的动画效果
2. 实现草的渐变消失而不是瞬间消失
3. 添加不同类型的草消失效果
4. 实现草状态的持久化保存
5. 添加音效和粒子效果

## 故障排除
1. **草没有消失**: 检查 grassState 字段是否正确传递到着色器
2. **UI不显示**: 确保场景中有EventSystem组件
3. **点击检测不工作**: 检查 groundLayerMask 设置和地面碰撞器
4. **性能问题**: 考虑减少草的数量或优化更新频率

## 技术细节
- 使用 `Graphics.DrawMeshInstancedIndirect` 进行实例化渲染
- 通过 `ComputeBuffer` 传递顶点数据到GPU
- 在着色器中通过 `grassState` 字段控制草的可见性
- 支持URP渲染管线的前向渲染和阴影投射
