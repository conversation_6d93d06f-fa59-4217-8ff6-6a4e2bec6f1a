
using System.Collections.Generic;
using System;
using UnityEngine;
using Random = UnityEngine.Random;

namespace GameWish.Game
{
    public class CS_MowGrass : MonoBehaviour
    {
        // 割草事件
        public event Action<Vector3, int, int> OnGrassMowed; // 位置, 割掉的草数量, 剩余草数量
        public event Action OnMowingStarted; // 开始割草
        public event Action OnMowingEnded; // 结束割草
        [Header("Grass Mesh Settings")]
        [SerializeField] private Mesh m_Grass;
        [SerializeField] private Shader m_GrassShader;
        [SerializeField] private Material m_GrassMaterial;

        [Header("Grass Generation Settings")]
        [SerializeField] private bool m_UseCustomGeneration = false;

        [Header("Generation Source")]
        [SerializeField] private GenerationMode m_GenerationMode = GenerationMode.Terrain;
        [SerializeField] private Terrain m_Terrain;
        [SerializeField] private Mesh m_CustomMesh;
        [SerializeField] private Transform m_CustomMeshTransform;

        [Header("Generation Parameters")]
        [SerializeField] private Vector2 m_GrassArea = new Vector2(10f, 10f);
        [SerializeField] private bool m_UseCustomMeshBounds = true; // 是否使用自定义Mesh的边界
        [SerializeField] private float m_GrassDensity = 1f;
        [SerializeField] private float m_GrassSpacing = 0.5f;
        [SerializeField] private float m_MinSlopeAngle = 0f;
        [SerializeField] private float m_MaxSlopeAngle = 45f;
        [SerializeField] private LayerMask m_GroundLayerMask = 1;
        [SerializeField] private float m_RaycastHeight = 10f;

        [Header("Grass Orientation")]
        [SerializeField] private GrassOrientationMode m_GrassOrientationMode = GrassOrientationMode.AlwaysUp;
        [SerializeField] private float m_NormalInfluence = 0.5f; // 法线影响程度 (0=完全向上, 1=完全跟随法线)

        public enum GrassOrientationMode
        {
            AlwaysUp,           // 草总是向上生长
            FollowSurfaceNormal, // 草跟随表面法线
            MixedOrientation    // 混合模式，部分跟随法线
        }

        public enum GenerationMode
        {
            Terrain,        // 使用Unity地形
            CustomMesh,     // 使用自定义Mesh
            Raycast        // 使用射线检测
        }

        [Header("Grass Appearance")]
        [SerializeField] private Color m_TopColor = Color.white;
        [SerializeField] private Color m_BottomColor = Color.white;

        [SerializeField] private Vector2 m_HeightRandom = new Vector2(0.8f, 1.4f);
        [SerializeField] private Vector2 m_WidthRandom = new Vector2(0.8f, 1.4f);
        [SerializeField] private float m_RotateRandom = 45f;

        [Header("Wave Effects")]
        [SerializeField] private Color m_WaveSinColor = Color.white;
        [SerializeField] private float m_WaveA = 0.2f;
        [SerializeField] private float m_WaveS = 1f;
        [SerializeField] private float m_WaveL = 10f;

        private Material m_Material;

        // 新增：草地控制相关字段
        [SerializeField] private int m_DisappearedGrassCount = 0;
        private List<bool> m_GrassStates; // 记录每根草的状态
        private List<SourceVertex> m_VerticesData; // 保存顶点数据用于更新

        // 空间划分优化相关字段
        [Header("Performance Optimization")]
        [SerializeField] private bool m_UseSpatialOptimization = true;
        [SerializeField] private float m_GridCellSize = 2f;
        private Dictionary<Vector2Int, List<int>> m_SpatialGrid; // 空间网格，存储每个格子中的草索引
        private Vector2 m_GridOffset; // 网格偏移
        private Vector2 m_GridSize; // 网格大小

        // 割草状态跟踪相关字段
        [Header("Mowing State Tracking")]
        [SerializeField] private bool m_TrackMowingState = true;
        private bool m_IsMowing = false; // 当前是否正在割草
        private float m_LastMowTime = 0f; // 上次割草时间
        private Vector3 m_LastMowPosition = Vector3.zero; // 上次割草位置
        private float m_MowingCooldown = 0.1f; // 割草冷却时间（秒）
        private int m_GrassCountBeforeMow = 0; // 割草前的草数量

        private struct SourceVertex
        {
            public Matrix4x4 mat;
            public Vector2 uv;
            public Vector2 sourceUV;
            public float grassState; // 0 = 显示, 1 = 消失

            public static int GetSize()
            {
                return sizeof(float) * (16 + 2 + 2 + 1);
            }
        };

        private bool m_Initialized;
        private ComputeBuffer m_DrawBuffer;
        private ComputeBuffer m_ArgsBuffer;

        private Bounds m_LocalBound;
        private uint[] m_Args = new uint[] { 0, 1, 0, 0, 0 };

        private void LateUpdate()
        {
            if (m_Initialized)
            {
                StartDraw();
            }
        }

        private void OnDestroy()
        {
            Release();
        }

        private void Start()
        {
            Initialize();
        }
        [ContextMenu("Init")]
        public void Initialize()
        {
            if (m_Initialized)
            {
                Release();
            }

            m_Material = new Material(m_GrassShader);
            m_Material.CopyPropertiesFromMaterial(m_GrassMaterial);

            m_VerticesData = new List<SourceVertex>();
            m_GrassStates = new List<bool>();


            GenerateCustomGrassPositions();


            if (m_VerticesData.Count == 0)
            {
                Debug.LogWarning("No grass positions generated!");
                return;
            }

            m_DrawBuffer = new ComputeBuffer(m_VerticesData.Count, SourceVertex.GetSize());
            m_DrawBuffer.SetData(m_VerticesData);

            m_Args[0] = (uint)m_Grass.GetIndexCount(0);
            m_Args[1] = (uint)m_VerticesData.Count;
            m_Args[2] = (uint)m_Grass.GetIndexStart(0);
            m_Args[3] = (uint)m_Grass.GetBaseVertex(0);
            m_Args[4] = (uint)0;

            m_ArgsBuffer = new ComputeBuffer(1, sizeof(uint) * m_Args.Length, ComputeBufferType.IndirectArguments);
            m_ArgsBuffer.SetData(m_Args);

            m_Material.SetBuffer("_DrawTriangles", m_DrawBuffer);

            // 设置边界
            if (m_UseCustomGeneration)
            {
                Vector2 actualArea = GetActualGrassArea();
                Vector3 actualCenter = GetAreaCenter();
                m_LocalBound = new Bounds(actualCenter, new Vector3(actualArea.x, 10f, actualArea.y));
            }
            else
            {
                m_LocalBound = new Bounds(transform.position, new Vector3(m_GrassArea.x, 10f, m_GrassArea.y));
            }

            // 初始化空间网格优化
            if (m_UseSpatialOptimization)
            {
                InitializeSpatialGrid();
            }

            m_Initialized = true;
        }

        /// <summary>
        /// 自定义生成草地位置，支持地形和坡度检测
        /// </summary>
        private void GenerateCustomGrassPositions()
        {
            // 获取实际的生成区域
            Vector2 actualGrassArea = GetActualGrassArea();
            Vector3 areaCenter = GetAreaCenter();

            Vector3 startPos = areaCenter - new Vector3(actualGrassArea.x * 0.5f, 0, actualGrassArea.y * 0.5f);

            int grassCountX = Mathf.RoundToInt(actualGrassArea.x / m_GrassSpacing * m_GrassDensity);
            int grassCountZ = Mathf.RoundToInt(actualGrassArea.y / m_GrassSpacing * m_GrassDensity);

            for (int x = 0; x < grassCountX; x++)
            {
                for (int z = 0; z < grassCountZ; z++)
                {
                    // 计算基础位置
                    Vector3 basePos = startPos + new Vector3(
                        x * m_GrassSpacing / m_GrassDensity,
                        0,
                        z * m_GrassSpacing / m_GrassDensity
                    );

                    // 添加随机偏移
                    Vector3 randomOffset = new Vector3(
                        Random.Range(-m_GrassSpacing * 0.3f, m_GrassSpacing * 0.3f),
                        0,
                        Random.Range(-m_GrassSpacing * 0.3f, m_GrassSpacing * 0.3f)
                    );

                    Vector3 samplePos = basePos + randomOffset;

                    // 获取地面位置和法线
                    if (GetGroundPositionAndNormal(samplePos, out Vector3 groundPos, out Vector3 normal))
                    {
                        // 检查坡度角度
                        float slopeAngle = Vector3.Angle(Vector3.up, normal);
                        if (slopeAngle >= m_MinSlopeAngle && slopeAngle <= m_MaxSlopeAngle)
                        {
                            CreateGrassAtPosition(groundPos, normal);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取实际的草地生成区域
        /// </summary>
        private Vector2 GetActualGrassArea()
        {
            if (m_GenerationMode == GenerationMode.CustomMesh && m_UseCustomMeshBounds && m_CustomMesh != null)
            {
                return GetCustomMeshBounds();
            }
            else if (m_GenerationMode == GenerationMode.Terrain && m_UseCustomMeshBounds && m_Terrain != null)
            {
                return GetTerrainBounds();
            }
            else
            {
                return m_GrassArea; // 使用手动设置的区域
            }
        }

        /// <summary>
        /// 获取区域中心点
        /// </summary>
        private Vector3 GetAreaCenter()
        {
            if (m_GenerationMode == GenerationMode.CustomMesh && m_UseCustomMeshBounds && m_CustomMesh != null)
            {
                return GetCustomMeshCenter();
            }
            else if (m_GenerationMode == GenerationMode.Terrain && m_UseCustomMeshBounds && m_Terrain != null)
            {
                return GetTerrainCenter();
            }
            else
            {
                return transform.position; // 使用脚本对象的位置
            }
        }

        /// <summary>
        /// 获取自定义Mesh的边界大小
        /// </summary>
        private Vector2 GetCustomMeshBounds()
        {
            if (m_CustomMesh == null) return m_GrassArea;

            Bounds bounds = m_CustomMesh.bounds;

            // 如果有Transform，应用缩放
            if (m_CustomMeshTransform != null)
            {
                Vector3 scale = m_CustomMeshTransform.lossyScale;
                bounds.size = Vector3.Scale(bounds.size, scale);
            }

            return new Vector2(bounds.size.x, bounds.size.z);
        }

        /// <summary>
        /// 获取自定义Mesh的中心点
        /// </summary>
        private Vector3 GetCustomMeshCenter()
        {
            if (m_CustomMesh == null) return transform.position;

            Vector3 center = m_CustomMesh.bounds.center;

            // 如果有Transform，应用变换
            if (m_CustomMeshTransform != null)
            {
                center = m_CustomMeshTransform.TransformPoint(center);
            }
            else
            {
                center += transform.position;
            }

            return center;
        }

        /// <summary>
        /// 获取地形的边界大小
        /// </summary>
        private Vector2 GetTerrainBounds()
        {
            if (m_Terrain == null) return m_GrassArea;

            Vector3 terrainSize = m_Terrain.terrainData.size;
            return new Vector2(terrainSize.x, terrainSize.z);
        }

        /// <summary>
        /// 获取地形的中心点
        /// </summary>
        private Vector3 GetTerrainCenter()
        {
            if (m_Terrain == null) return transform.position;

            Vector3 terrainSize = m_Terrain.terrainData.size;
            return m_Terrain.transform.position + new Vector3(terrainSize.x * 0.5f, 0, terrainSize.z * 0.5f);
        }

        /// <summary>
        /// 获取地面位置和法线
        /// </summary>
        private bool GetGroundPositionAndNormal(Vector3 samplePos, out Vector3 groundPos, out Vector3 normal)
        {
            groundPos = Vector3.zero;
            normal = Vector3.up;

            switch (m_GenerationMode)
            {
                case GenerationMode.Terrain:
                    if (m_Terrain != null)
                        return GetTerrainPositionAndNormal(samplePos, out groundPos, out normal);
                    break;

                case GenerationMode.CustomMesh:
                    if (m_CustomMesh != null)
                        return GetCustomMeshPositionAndNormal(samplePos, out groundPos, out normal);
                    break;

                case GenerationMode.Raycast:
                    return GetRaycastPositionAndNormal(samplePos, out groundPos, out normal);
            }

            // 如果所有方法都失败，尝试射线检测作为后备
            return GetRaycastPositionAndNormal(samplePos, out groundPos, out normal);
        }

        /// <summary>
        /// 从地形获取位置和法线
        /// </summary>
        private bool GetTerrainPositionAndNormal(Vector3 samplePos, out Vector3 groundPos, out Vector3 normal)
        {
            groundPos = Vector3.zero;
            normal = Vector3.up;

            // 将世界坐标转换为地形本地坐标
            Vector3 terrainPos = samplePos - m_Terrain.transform.position;

            // 检查是否在地形范围内
            if (terrainPos.x < 0 || terrainPos.x > m_Terrain.terrainData.size.x ||
                terrainPos.z < 0 || terrainPos.z > m_Terrain.terrainData.size.z)
            {
                return false;
            }

            // 获取地形高度
            float height = m_Terrain.SampleHeight(samplePos);
            groundPos = new Vector3(samplePos.x, height, samplePos.z);

            // 获取地形法线
            Vector3 terrainLocalPos = terrainPos;
            terrainLocalPos.x /= m_Terrain.terrainData.size.x;
            terrainLocalPos.z /= m_Terrain.terrainData.size.z;

            normal = m_Terrain.terrainData.GetInterpolatedNormal(terrainLocalPos.x, terrainLocalPos.z);
            normal = m_Terrain.transform.TransformDirection(normal);

            return true;
        }

        /// <summary>
        /// 从自定义Mesh获取位置和法线
        /// </summary>
        private bool GetCustomMeshPositionAndNormal(Vector3 samplePos, out Vector3 groundPos, out Vector3 normal)
        {
            groundPos = Vector3.zero;
            normal = Vector3.up;

            if (m_CustomMesh == null)
                return false;

            // 获取Mesh的变换矩阵
            Matrix4x4 meshMatrix = m_CustomMeshTransform != null ?
                m_CustomMeshTransform.localToWorldMatrix :
                Matrix4x4.identity;

            // 将采样点转换到Mesh的本地空间
            Vector3 localSamplePos = meshMatrix.inverse.MultiplyPoint3x4(samplePos);

            // 获取Mesh数据
            Vector3[] vertices = m_CustomMesh.vertices;
            int[] triangles = m_CustomMesh.triangles;
            Vector3[] normals = m_CustomMesh.normals;

            float closestDistance = float.MaxValue;
            bool foundPoint = false;

            // 遍历所有三角形，找到最近的表面点
            for (int i = 0; i < triangles.Length; i += 3)
            {
                Vector3 v0 = vertices[triangles[i]];
                Vector3 v1 = vertices[triangles[i + 1]];
                Vector3 v2 = vertices[triangles[i + 2]];

                // 计算三角形的重心坐标投影点
                Vector3 projectedPoint = GetClosestPointOnTriangle(localSamplePos, v0, v1, v2);
                float distance = Vector3.Distance(localSamplePos, projectedPoint);

                if (distance < closestDistance)
                {
                    closestDistance = distance;

                    // 将本地坐标转换回世界坐标
                    groundPos = meshMatrix.MultiplyPoint3x4(projectedPoint);

                    // 计算插值法线
                    Vector3 localNormal = GetInterpolatedNormal(localSamplePos, v0, v1, v2,
                        normals[triangles[i]], normals[triangles[i + 1]], normals[triangles[i + 2]]);
                    normal = meshMatrix.MultiplyVector(localNormal).normalized;

                    foundPoint = true;
                }
            }

            return foundPoint && closestDistance < m_GrassSpacing * 2f; // 限制最大距离
        }

        /// <summary>
        /// 获取点在三角形上的最近点
        /// </summary>
        private Vector3 GetClosestPointOnTriangle(Vector3 point, Vector3 a, Vector3 b, Vector3 c)
        {
            Vector3 ab = b - a;
            Vector3 ac = c - a;
            Vector3 ap = point - a;

            float d1 = Vector3.Dot(ab, ap);
            float d2 = Vector3.Dot(ac, ap);
            if (d1 <= 0f && d2 <= 0f) return a;

            Vector3 bp = point - b;
            float d3 = Vector3.Dot(ab, bp);
            float d4 = Vector3.Dot(ac, bp);
            if (d3 >= 0f && d4 <= d3) return b;

            Vector3 cp = point - c;
            float d5 = Vector3.Dot(ab, cp);
            float d6 = Vector3.Dot(ac, cp);
            if (d6 >= 0f && d5 <= d6) return c;

            float vc = d1 * d4 - d3 * d2;
            if (vc <= 0f && d1 >= 0f && d3 <= 0f)
            {
                float v = d1 / (d1 - d3);
                return a + v * ab;
            }

            float vb = d5 * d2 - d1 * d6;
            if (vb <= 0f && d2 >= 0f && d6 <= 0f)
            {
                float w = d2 / (d2 - d6);
                return a + w * ac;
            }

            float va = d3 * d6 - d5 * d4;
            if (va <= 0f && (d4 - d3) >= 0f && (d5 - d6) >= 0f)
            {
                float w = (d4 - d3) / ((d4 - d3) + (d5 - d6));
                return b + w * (c - b);
            }

            float denom = 1f / (va + vb + vc);
            float v2 = vb * denom;
            float w2 = vc * denom;
            return a + ab * v2 + ac * w2;
        }

        /// <summary>
        /// 获取插值法线
        /// </summary>
        private Vector3 GetInterpolatedNormal(Vector3 point, Vector3 a, Vector3 b, Vector3 c,
            Vector3 normalA, Vector3 normalB, Vector3 normalC)
        {
            // 计算重心坐标
            Vector3 v0 = b - a;
            Vector3 v1 = c - a;
            Vector3 v2 = point - a;

            float dot00 = Vector3.Dot(v0, v0);
            float dot01 = Vector3.Dot(v0, v1);
            float dot02 = Vector3.Dot(v0, v2);
            float dot11 = Vector3.Dot(v1, v1);
            float dot12 = Vector3.Dot(v1, v2);

            float invDenom = 1f / (dot00 * dot11 - dot01 * dot01);
            float u = (dot11 * dot02 - dot01 * dot12) * invDenom;
            float v = (dot00 * dot12 - dot01 * dot02) * invDenom;
            float w = 1f - u - v;

            // 插值法线
            return (normalA * w + normalB * u + normalC * v).normalized;
        }

        /// <summary>
        /// 通过射线检测获取位置和法线
        /// </summary>
        private bool GetRaycastPositionAndNormal(Vector3 samplePos, out Vector3 groundPos, out Vector3 normal)
        {
            groundPos = Vector3.zero;
            normal = Vector3.up;

            Vector3 rayStart = new Vector3(samplePos.x, samplePos.y + m_RaycastHeight, samplePos.z);

            if (Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, m_RaycastHeight * 2f, m_GroundLayerMask))
            {
                groundPos = hit.point;
                normal = hit.normal;
                return true;
            }

            return false;
        }

        /// <summary>
        /// 初始化空间网格系统
        /// </summary>
        private void InitializeSpatialGrid()
        {
            if (m_VerticesData == null || m_VerticesData.Count == 0) return;

            m_SpatialGrid = new Dictionary<Vector2Int, List<int>>();

            // 计算网格范围
            Vector3 minPos = Vector3.positiveInfinity;
            Vector3 maxPos = Vector3.negativeInfinity;

            // 找到所有草的边界
            for (int i = 0; i < m_VerticesData.Count; i++)
            {
                Vector3 grassPos = new Vector3(
                    m_VerticesData[i].mat.m03,
                    m_VerticesData[i].mat.m13,
                    m_VerticesData[i].mat.m23
                );

                minPos = Vector3.Min(minPos, grassPos);
                maxPos = Vector3.Max(maxPos, grassPos);
            }

            // 设置网格偏移和大小
            m_GridOffset = new Vector2(minPos.x, minPos.z);
            m_GridSize = new Vector2(maxPos.x - minPos.x, maxPos.z - minPos.z);

            // 将每根草分配到对应的网格格子中
            for (int i = 0; i < m_VerticesData.Count; i++)
            {
                Vector3 grassPos = new Vector3(
                    m_VerticesData[i].mat.m03,
                    m_VerticesData[i].mat.m13,
                    m_VerticesData[i].mat.m23
                );

                Vector2Int gridCoord = WorldToGridCoord(grassPos);

                if (!m_SpatialGrid.ContainsKey(gridCoord))
                {
                    m_SpatialGrid[gridCoord] = new List<int>();
                }

                m_SpatialGrid[gridCoord].Add(i);
            }

            Debug.Log($"Spatial grid initialized: {m_SpatialGrid.Count} cells, {m_VerticesData.Count} grass instances");
        }

        /// <summary>
        /// 将世界坐标转换为网格坐标
        /// </summary>
        private Vector2Int WorldToGridCoord(Vector3 worldPos)
        {
            int x = Mathf.FloorToInt((worldPos.x - m_GridOffset.x) / m_GridCellSize);
            int z = Mathf.FloorToInt((worldPos.z - m_GridOffset.y) / m_GridCellSize);
            return new Vector2Int(x, z);
        }

        /// <summary>
        /// 获取指定区域内的所有网格格子
        /// </summary>
        private List<Vector2Int> GetGridCellsInRadius(Vector3 center, float radius)
        {
            List<Vector2Int> cells = new List<Vector2Int>();

            Vector2Int centerGrid = WorldToGridCoord(center);
            int gridRadius = Mathf.CeilToInt(radius / m_GridCellSize);

            for (int x = centerGrid.x - gridRadius; x <= centerGrid.x + gridRadius; x++)
            {
                for (int z = centerGrid.y - gridRadius; z <= centerGrid.y + gridRadius; z++)
                {
                    Vector2Int gridCoord = new Vector2Int(x, z);
                    if (m_SpatialGrid.ContainsKey(gridCoord))
                    {
                        cells.Add(gridCoord);
                    }
                }
            }

            return cells;
        }

        /// <summary>
        /// 在指定位置创建草
        /// </summary>
        private void CreateGrassAtPosition(Vector3 position, Vector3 normal)
        {
            Vector3 scale = new Vector3(
                Random.Range(m_WidthRandom.x, m_WidthRandom.y),
                Random.Range(m_HeightRandom.x, m_HeightRandom.y),
                1
            );

            Quaternion rot = CalculateGrassRotation(normal);

            m_VerticesData.Add(new SourceVertex()
            {
                mat = Matrix4x4.TRS(position, rot, scale),
                uv = new Vector2(0, 0),
                sourceUV = new Vector2(Random.value, Random.value), // 随机UV用于变化
                grassState = 0.0f
            });

            m_GrassStates.Add(false);
        }

        /// <summary>
        /// 根据朝向模式计算草的旋转
        /// </summary>
        private Quaternion CalculateGrassRotation(Vector3 surfaceNormal)
        {
            Quaternion baseRotation;

            switch (m_GrassOrientationMode)
            {
                case GrassOrientationMode.AlwaysUp:
                    // 草总是向上生长，只有随机Y轴旋转
                    baseRotation = Quaternion.identity;
                    break;

                case GrassOrientationMode.FollowSurfaceNormal:
                    // 草完全跟随表面法线
                    baseRotation = Quaternion.FromToRotation(Vector3.up, surfaceNormal);
                    break;

                case GrassOrientationMode.MixedOrientation:
                    // 混合模式：在向上和法线之间插值
                    Vector3 targetUp = Vector3.Slerp(Vector3.up, surfaceNormal, m_NormalInfluence);
                    baseRotation = Quaternion.FromToRotation(Vector3.up, targetUp);
                    break;

                default:
                    baseRotation = Quaternion.identity;
                    break;
            }

            // 添加随机Y轴旋转
            Quaternion randomYRotation = Quaternion.Euler(0, Random.Range(-m_RotateRandom, m_RotateRandom), 0);

            return baseRotation * randomYRotation;
        }

        public void SetMainTexture(Texture2D value)
        {
            if (m_Material != null)
            {
                m_Material.SetTexture("_MainTex", value);
            }
        }

        // 获取消失草的数量
        public int GetDisappearedGrassCount()
        {
            return m_DisappearedGrassCount;
        }
        public int GetTotalGrassCount()
        {
            return m_VerticesData.Count;
        }

        public float GetDisappearedGrassRatio()
        {
            return (float)m_DisappearedGrassCount / m_VerticesData.Count;
        }

        /// <summary>
        /// 检查当前是否正在割草
        /// </summary>
        public bool IsMowing()
        {
            return m_IsMowing;
        }

        /// <summary>
        /// 获取上次割草的时间
        /// </summary>
        public float GetLastMowTime()
        {
            return m_LastMowTime;
        }

        /// <summary>
        /// 获取上次割草的位置
        /// </summary>
        public Vector3 GetLastMowPosition()
        {
            return m_LastMowPosition;
        }

        /// <summary>
        /// 获取距离上次割草的时间间隔
        /// </summary>
        public float GetTimeSinceLastMow()
        {
            return Time.time - m_LastMowTime;
        }

        /// <summary>
        /// 检查是否在冷却时间内
        /// </summary>
        public bool IsInMowingCooldown()
        {
            return GetTimeSinceLastMow() < m_MowingCooldown;
        }

        /// <summary>
        /// 设置割草冷却时间
        /// </summary>
        public void SetMowingCooldown(float cooldown)
        {
            m_MowingCooldown = Mathf.Max(0f, cooldown);
        }

        /// <summary>
        /// 检查指定位置是否可以割草（不在冷却中且有草）
        /// </summary>
        public bool CanMowAtPosition(Vector3 position, float radius)
        {
            if (IsInMowingCooldown()) return false;

            // 检查该区域是否有草可以割
            return HasGrassInArea(position, radius);
        }

        /// <summary>
        /// 检查指定区域是否有草存在
        /// </summary>
        private bool HasGrassInArea(Vector3 center, float radius)
        {
            if (m_GrassStates == null || m_VerticesData == null) return false;

            if (m_UseSpatialOptimization && m_SpatialGrid != null)
            {
                // 使用空间网格优化检查
                List<Vector2Int> affectedCells = GetGridCellsInRadius(center, radius);

                foreach (Vector2Int cellCoord in affectedCells)
                {
                    if (m_SpatialGrid.ContainsKey(cellCoord))
                    {
                        List<int> grassIndicesInCell = m_SpatialGrid[cellCoord];

                        for (int j = 0; j < grassIndicesInCell.Count; j++)
                        {
                            int grassIndex = grassIndicesInCell[j];

                            if (grassIndex < m_GrassStates.Count && !m_GrassStates[grassIndex])
                            {
                                Vector3 grassWorldPos = new Vector3(
                                    m_VerticesData[grassIndex].mat.m03,
                                    m_VerticesData[grassIndex].mat.m13,
                                    m_VerticesData[grassIndex].mat.m23
                                );

                                float distance = Vector3.Distance(grassWorldPos, center);
                                if (distance <= radius)
                                {
                                    return true; // 找到了草
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                // 使用原始遍历方法检查
                for (int i = 0; i < m_GrassStates.Count && i < m_VerticesData.Count; i++)
                {
                    if (!m_GrassStates[i])
                    {
                        Vector3 grassWorldPos = new Vector3(
                            m_VerticesData[i].mat.m03,
                            m_VerticesData[i].mat.m13,
                            m_VerticesData[i].mat.m23
                        );

                        float distance = Vector3.Distance(grassWorldPos, center);
                        if (distance <= radius)
                        {
                            return true; // 找到了草
                        }
                    }
                }
            }

            return false; // 没有找到草
        }

        // 让指定索引的草消失
        public void MakeGrassDisappear(int grassIndex)
        {
            if (grassIndex >= 0 && grassIndex < m_GrassStates.Count && !m_GrassStates[grassIndex])
            {
                m_GrassStates[grassIndex] = true;
                var vertex = m_VerticesData[grassIndex];
                vertex.grassState = 1.0f; // 标记为消失
                m_VerticesData[grassIndex] = vertex;
                m_DisappearedGrassCount++;
                UpdateGrassBuffer();
            }
        }

        // 让指定区域内的草消失（优化版本）
        public void MakeGrassDisappearInArea(Vector3 center, float radius)
        {
            if (m_GrassStates == null || m_VerticesData == null) return;

            // 开始割草状态跟踪
            if (m_TrackMowingState)
            {
                StartMowing(center);
            }

            int disappearedCount = 0;

            if (m_UseSpatialOptimization && m_SpatialGrid != null)
            {
                // 使用空间网格优化
                disappearedCount = MakeGrassDisappearInAreaOptimized(center, radius);
            }
            else
            {
                // 使用原始的遍历方法
                disappearedCount = MakeGrassDisappearInAreaBruteForce(center, radius);
            }

            m_DisappearedGrassCount += disappearedCount;
            UpdateGrassBuffer();

            // 结束割草状态跟踪
            if (m_TrackMowingState)
            {
                EndMowing(disappearedCount);
            }
        }

        /// <summary>
        /// 优化版本：使用空间网格加速草地切割
        /// </summary>
        private int MakeGrassDisappearInAreaOptimized(Vector3 center, float radius)
        {
            int disappearedCount = 0;

            // 获取影响范围内的所有网格格子
            List<Vector2Int> affectedCells = GetGridCellsInRadius(center, radius);

            // 只检查这些格子中的草
            foreach (Vector2Int cellCoord in affectedCells)
            {
                if (m_SpatialGrid.ContainsKey(cellCoord))
                {
                    List<int> grassIndicesInCell = m_SpatialGrid[cellCoord];

                    for (int j = 0; j < grassIndicesInCell.Count; j++)
                    {
                        int grassIndex = grassIndicesInCell[j];

                        if (grassIndex < m_GrassStates.Count && !m_GrassStates[grassIndex])
                        {
                            // 从变换矩阵中提取位置
                            Vector3 grassWorldPos = new Vector3(
                                m_VerticesData[grassIndex].mat.m03,
                                m_VerticesData[grassIndex].mat.m13,
                                m_VerticesData[grassIndex].mat.m23
                            );

                            float distance = Vector3.Distance(grassWorldPos, center);

                            if (distance <= radius)
                            {
                                m_GrassStates[grassIndex] = true;
                                var vertex = m_VerticesData[grassIndex];
                                vertex.grassState = 1.0f;
                                m_VerticesData[grassIndex] = vertex;
                                disappearedCount++;
                            }
                        }
                    }
                }
            }

            return disappearedCount;
        }

        /// <summary>
        /// 原始版本：遍历所有草进行切割检查
        /// </summary>
        private int MakeGrassDisappearInAreaBruteForce(Vector3 center, float radius)
        {
            int disappearedCount = 0;

            for (int i = 0; i < m_GrassStates.Count && i < m_VerticesData.Count; i++)
            {
                if (!m_GrassStates[i]) // 只处理还存在的草
                {
                    // 从变换矩阵中提取位置
                    Vector3 grassWorldPos = new Vector3(
                        m_VerticesData[i].mat.m03,
                        m_VerticesData[i].mat.m13,
                        m_VerticesData[i].mat.m23
                    );

                    float distance = Vector3.Distance(grassWorldPos, center);

                    if (distance <= radius)
                    {
                        m_GrassStates[i] = true;
                        var vertex = m_VerticesData[i];
                        vertex.grassState = 1.0f;
                        m_VerticesData[i] = vertex;
                        disappearedCount++;
                    }
                }
            }

            return disappearedCount;
        }

        /// <summary>
        /// 开始割草状态跟踪
        /// </summary>
        private void StartMowing(Vector3 mowPosition)
        {
            if (!m_IsMowing)
            {
                m_IsMowing = true;
                m_GrassCountBeforeMow = GetRemainingGrassCount();
                OnMowingStarted?.Invoke();
            }

            m_LastMowTime = Time.time;
            m_LastMowPosition = mowPosition;
        }

        /// <summary>
        /// 结束割草状态跟踪
        /// </summary>
        private void EndMowing(int grassMowed)
        {
            // 触发割草事件（如果有草被割掉）
            if (grassMowed > 0)
            {
                OnGrassMowed?.Invoke(m_LastMowPosition, grassMowed, GetRemainingGrassCount());
            }

            // 设置冷却时间后结束割草状态
            Invoke(nameof(EndMowingDelayed), m_MowingCooldown);
        }

        /// <summary>
        /// 延迟结束割草状态
        /// </summary>
        private void EndMowingDelayed()
        {
            if (Time.time - m_LastMowTime >= m_MowingCooldown)
            {
                m_IsMowing = false;
                OnMowingEnded?.Invoke();
            }
        }

        // 恢复所有草
        [ContextMenu("Restore All Grass")]
        public void RestoreAllGrass()
        {
            if (m_GrassStates == null || m_VerticesData == null) return;

            for (int i = 0; i < m_GrassStates.Count && i < m_VerticesData.Count; i++)
            {
                m_GrassStates[i] = false;
                var vertex = m_VerticesData[i];
                vertex.grassState = 0.0f;
                m_VerticesData[i] = vertex;
            }
            m_DisappearedGrassCount = 0;
            UpdateGrassBuffer();
        }

        // 更新GPU缓冲区
        private void UpdateGrassBuffer()
        {
            if (m_DrawBuffer != null && m_VerticesData != null)
            {
                m_DrawBuffer.SetData(m_VerticesData);
            }
        }

        private void Release()
        {
            if (m_Initialized)
            {
                m_DrawBuffer.Release();
                m_ArgsBuffer.Release();
                DestroyImmediate(m_Material);
            }

            // 清理空间网格
            if (m_SpatialGrid != null)
            {
                m_SpatialGrid.Clear();
                m_SpatialGrid = null;
            }

            m_Initialized = false;
        }

        private void StartDraw()
        {
            m_ArgsBuffer.SetData(m_Args);

            m_Material.SetColor("_TopColor", m_TopColor);
            m_Material.SetColor("_BottomColor", m_BottomColor);

            m_Material.SetColor("_WaveSinColor", m_WaveSinColor);
            m_Material.SetFloat("_WaveA", m_WaveA);
            m_Material.SetFloat("_WaveL", m_WaveL);
            m_Material.SetFloat("_WaveS", m_WaveS);

            Graphics.DrawMeshInstancedIndirect(m_Grass, 0, m_Material, m_LocalBound, m_ArgsBuffer);
        }

        /// <summary>
        /// 重新生成草地（在运行时调用）
        /// </summary>
        [ContextMenu("Regenerate Grass")]
        public void RegenerateGrass()
        {
            Initialize();
        }

        /// <summary>
        /// 获取剩余草的数量
        /// </summary>
        public int GetRemainingGrassCount()
        {
            return GetTotalGrassCount() - m_DisappearedGrassCount;
        }

        /// <summary>
        /// 设置草地区域大小（仅在自定义生成模式下有效）
        /// </summary>
        public void SetGrassArea(Vector2 area)
        {
            m_GrassArea = area;
            if (m_UseCustomGeneration && Application.isPlaying)
            {
                RegenerateGrass();
            }
        }

        /// <summary>
        /// 设置草地密度（仅在自定义生成模式下有效）
        /// </summary>
        public void SetGrassDensity(float density)
        {
            m_GrassDensity = Mathf.Max(0.1f, density);
            if (m_UseCustomGeneration && Application.isPlaying)
            {
                RegenerateGrass();
            }
        }

        /// <summary>
        /// 设置生成模式
        /// </summary>
        public void SetGenerationMode(GenerationMode mode)
        {
            m_GenerationMode = mode;
            if (m_UseCustomGeneration && Application.isPlaying)
            {
                RegenerateGrass();
            }
        }

        /// <summary>
        /// 设置自定义Mesh
        /// </summary>
        public void SetCustomMesh(Mesh mesh, Transform meshTransform = null)
        {
            m_CustomMesh = mesh;
            m_CustomMeshTransform = meshTransform;
            if (m_UseCustomGeneration && m_GenerationMode == GenerationMode.CustomMesh && Application.isPlaying)
            {
                RegenerateGrass();
            }
        }

        /// <summary>
        /// 设置地形
        /// </summary>
        public void SetTerrain(Terrain terrain)
        {
            m_Terrain = terrain;
            if (m_UseCustomGeneration && m_GenerationMode == GenerationMode.Terrain && Application.isPlaying)
            {
                RegenerateGrass();
            }
        }

        /// <summary>
        /// 设置坡度角度范围
        /// </summary>
        public void SetSlopeAngleRange(float minAngle, float maxAngle)
        {
            m_MinSlopeAngle = Mathf.Clamp(minAngle, 0f, 90f);
            m_MaxSlopeAngle = Mathf.Clamp(maxAngle, minAngle, 90f);
            if (m_UseCustomGeneration && Application.isPlaying)
            {
                RegenerateGrass();
            }
        }

        /// <summary>
        /// 设置是否使用自定义Mesh边界
        /// </summary>
        public void SetUseCustomMeshBounds(bool useCustomBounds)
        {
            m_UseCustomMeshBounds = useCustomBounds;
            if (m_UseCustomGeneration && Application.isPlaying)
            {
                RegenerateGrass();
            }
        }

        /// <summary>
        /// 获取当前实际使用的草地区域大小
        /// </summary>
        public Vector2 GetCurrentGrassArea()
        {
            return GetActualGrassArea();
        }

        /// <summary>
        /// 获取当前实际使用的区域中心
        /// </summary>
        public Vector3 GetCurrentAreaCenter()
        {
            return GetAreaCenter();
        }

        /// <summary>
        /// 设置是否使用空间优化
        /// </summary>
        public void SetUseSpatialOptimization(bool useSpatialOptimization)
        {
            if (m_UseSpatialOptimization != useSpatialOptimization)
            {
                m_UseSpatialOptimization = useSpatialOptimization;

                if (m_UseSpatialOptimization && m_Initialized && m_VerticesData != null)
                {
                    InitializeSpatialGrid();
                }
                else if (!m_UseSpatialOptimization && m_SpatialGrid != null)
                {
                    m_SpatialGrid.Clear();
                    m_SpatialGrid = null;
                }
            }
        }

        /// <summary>
        /// 设置网格格子大小
        /// </summary>
        public void SetGridCellSize(float cellSize)
        {
            if (cellSize > 0 && m_GridCellSize != cellSize)
            {
                m_GridCellSize = cellSize;

                if (m_UseSpatialOptimization && m_Initialized && m_VerticesData != null)
                {
                    InitializeSpatialGrid(); // 重新初始化网格
                }
            }
        }

        /// <summary>
        /// 设置草地朝向模式
        /// </summary>
        public void SetGrassOrientationMode(GrassOrientationMode mode)
        {
            if (m_GrassOrientationMode != mode)
            {
                m_GrassOrientationMode = mode;
                if (m_UseCustomGeneration && Application.isPlaying)
                {
                    RegenerateGrass();
                }
            }
        }

        /// <summary>
        /// 设置法线影响程度（仅在混合模式下有效）
        /// </summary>
        public void SetNormalInfluence(float influence)
        {
            influence = Mathf.Clamp01(influence);
            if (m_NormalInfluence != influence)
            {
                m_NormalInfluence = influence;
                if (m_UseCustomGeneration && m_GrassOrientationMode == GrassOrientationMode.MixedOrientation && Application.isPlaying)
                {
                    RegenerateGrass();
                }
            }
        }

        /// <summary>
        /// 获取当前草地朝向模式
        /// </summary>
        public GrassOrientationMode GetGrassOrientationMode()
        {
            return m_GrassOrientationMode;
        }

        /// <summary>
        /// 获取空间网格统计信息
        /// </summary>
        public string GetSpatialGridStats()
        {
            if (!m_UseSpatialOptimization || m_SpatialGrid == null)
            {
                return "Spatial optimization is disabled";
            }

            int totalCells = m_SpatialGrid.Count;
            int totalGrass = m_VerticesData?.Count ?? 0;
            int emptyCells = 0;
            int maxGrassInCell = 0;
            int minGrassInCell = int.MaxValue;

            foreach (var kvp in m_SpatialGrid)
            {
                int grassCount = kvp.Value.Count;
                if (grassCount == 0)
                {
                    emptyCells++;
                }
                else
                {
                    maxGrassInCell = Mathf.Max(maxGrassInCell, grassCount);
                    minGrassInCell = Mathf.Min(minGrassInCell, grassCount);
                }
            }

            if (minGrassInCell == int.MaxValue) minGrassInCell = 0;

            return $"Spatial Grid Stats:\n" +
                   $"- Total Cells: {totalCells}\n" +
                   $"- Empty Cells: {emptyCells}\n" +
                   $"- Cell Size: {m_GridCellSize}m\n" +
                   $"- Total Grass: {totalGrass}\n" +
                   $"- Grass per Cell: {minGrassInCell}-{maxGrassInCell}\n" +
                   $"- Grid Area: {m_GridSize.x:F1}x{m_GridSize.y:F1}m";
        }

        /// <summary>
        /// 在编辑器中预览草地生成区域
        /// </summary>
        private void OnDrawGizmosSelected()
        {
            if (m_UseCustomGeneration)
            {
                switch (m_GenerationMode)
                {
                    case GenerationMode.Terrain:
                        if (m_Terrain != null)
                        {
                            Gizmos.color = Color.green;
                            Vector3 terrainSize = m_Terrain.terrainData.size;
                            Vector3 terrainCenter = m_Terrain.transform.position + terrainSize * 0.5f;
                            Gizmos.DrawWireCube(terrainCenter, terrainSize);
                        }
                        break;

                    case GenerationMode.CustomMesh:
                        if (m_CustomMesh != null)
                        {
                            Gizmos.color = Color.cyan;
                            Matrix4x4 meshMatrix = m_CustomMeshTransform != null ?
                                m_CustomMeshTransform.localToWorldMatrix :
                                Matrix4x4.identity;

                            Gizmos.matrix = meshMatrix;
                            Gizmos.DrawWireMesh(m_CustomMesh);
                            Gizmos.matrix = Matrix4x4.identity;

                            // 显示实际生成区域
                            Vector2 actualArea = GetActualGrassArea();
                            Vector3 actualCenter = GetAreaCenter();
                            Gizmos.color = Color.yellow;
                            Gizmos.DrawWireCube(actualCenter, new Vector3(actualArea.x, 1f, actualArea.y));

                            // 如果不使用Mesh边界，显示手动设置的区域
                            if (!m_UseCustomMeshBounds)
                            {
                                Gizmos.color = Color.red;
                                Gizmos.DrawWireCube(transform.position, new Vector3(m_GrassArea.x, 0.5f, m_GrassArea.y));
                            }
                        }
                        break;

                    case GenerationMode.Raycast:
                        Vector2 raycastArea = GetActualGrassArea();
                        Vector3 raycastCenter = GetAreaCenter();

                        Gizmos.color = Color.green;
                        Gizmos.DrawWireCube(raycastCenter, new Vector3(raycastArea.x, 1f, raycastArea.y));

                        Gizmos.color = Color.yellow;
                        Gizmos.DrawWireCube(raycastCenter, new Vector3(raycastArea.x * 0.9f, 0.5f, raycastArea.y * 0.9f));
                        break;
                }
            }
        }
    }
}