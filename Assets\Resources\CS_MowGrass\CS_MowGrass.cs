
using System.Collections.Generic;

using UnityEngine;
using Random = UnityEngine.Random;

namespace GameWish.Game
{
    public class CS_MowGrass : MonoBehaviour
    {
        [SerializeField] private Mesh m_Grass;
        [SerializeField] private Mesh m_GrassSourceMesh;
        [SerializeField] private Shader m_GrassShader;
        [SerializeField] private Material m_GrassMaterial;

        [SerializeField] private Color m_TopColor = Color.white;
        [SerializeField] private Color m_BottomColor = Color.white;

        [SerializeField] private Vector2 m_HeightRandom = new Vector2(0.8f, 1.4f);
        [SerializeField] private Vector2 m_WidthRandom = new Vector2(0.8f, 1.4f);
        [SerializeField] private float m_RotateRandom = 45f;

        [SerializeField] private Color m_WaveSinColor = Color.white;
        [SerializeField] private float m_WaveA = 0.2f;
        [SerializeField] private float m_WaveS = 1f;
        [SerializeField] private float m_WaveL = 10f;

        private Material m_Material;

        // 新增：草地控制相关字段
        [SerializeField] private int m_DisappearedGrassCount = 0;
        private List<bool> m_GrassStates; // 记录每根草的状态
        private List<SourceVertex> m_VerticesData; // 保存顶点数据用于更新

        private struct SourceVertex
        {
            public Matrix4x4 mat;
            public Vector2 uv;
            public Vector2 sourceUV;
            public float grassState; // 0 = 显示, 1 = 消失

            public static int GetSize()
            {
                return sizeof(float) * (16 + 2 + 2 + 1);
            }
        };

        private bool m_Initialized;
        private ComputeBuffer m_DrawBuffer;
        private ComputeBuffer m_ArgsBuffer;

        private Bounds m_LocalBound;
        private uint[] m_Args = new uint[] { 0, 1, 0, 0, 0 };

        private void LateUpdate()
        {
            if (m_Initialized)
            {
                StartDraw();
            }
        }

        private void OnDestroy()
        {
            Release();
        }

        private void Start()
        {
            Initialize();
        }
        [ContextMenu("Init")]
        public void Initialize()
        {
            if (m_Initialized)
            {
                Release();
            }

            m_Material = new Material(m_GrassShader);
            m_Material.CopyPropertiesFromMaterial(m_GrassMaterial);

            Vector3[] positions = m_GrassSourceMesh.vertices;
            Vector2[] uvs = m_GrassSourceMesh.uv;

            m_VerticesData = new List<SourceVertex>();
            m_GrassStates = new List<bool>();

            for (int i = 0; i < positions.Length; i++)
            {
                Vector4 pos = transform.localToWorldMatrix * new Vector4(positions[i].x, positions[i].y, positions[i].z, 1);
                Vector3 scale = new Vector3(Random.Range(m_WidthRandom.x, m_WidthRandom.y), Random.Range(m_HeightRandom.x, m_HeightRandom.y), 1);
                Quaternion rot = Quaternion.Euler(0, Random.Range(-m_RotateRandom, m_RotateRandom), 0);

                m_VerticesData.Add(new SourceVertex()
                {
                    mat = Matrix4x4.TRS(pos, rot, scale),
                    uv = new Vector2(0, 0),
                    sourceUV = uvs[i],
                    grassState = 0.0f // 初始状态为显示
                });

                m_GrassStates.Add(false); // 初始状态为未消失
            }

            m_DrawBuffer = new ComputeBuffer(positions.Length, SourceVertex.GetSize());
            m_DrawBuffer.SetData(m_VerticesData);

            m_Args[0] = (uint)m_Grass.GetIndexCount(0);
            m_Args[1] = (uint)positions.Length;
            m_Args[2] = (uint)m_Grass.GetIndexStart(0);
            m_Args[3] = (uint)m_Grass.GetBaseVertex(0);
            m_Args[4] = (uint)0;

            m_ArgsBuffer = new ComputeBuffer(1, sizeof(uint) * m_Args.Length, ComputeBufferType.IndirectArguments);
            m_ArgsBuffer.SetData(m_Args);

            m_Material.SetBuffer("_DrawTriangles", m_DrawBuffer);

            m_LocalBound = m_GrassSourceMesh.bounds;
            m_LocalBound.center = transform.position;

            m_Initialized = true;
        }

        public void SetMainTexture(Texture2D value)
        {
            if (m_Material != null)
            {
                m_Material.SetTexture("_MainTex", value);
            }
        }

        // 获取消失草的数量
        public int GetDisappearedGrassCount()
        {
            return m_DisappearedGrassCount;
        }

        // 让指定索引的草消失
        public void MakeGrassDisappear(int grassIndex)
        {
            if (grassIndex >= 0 && grassIndex < m_GrassStates.Count && !m_GrassStates[grassIndex])
            {
                m_GrassStates[grassIndex] = true;
                var vertex = m_VerticesData[grassIndex];
                vertex.grassState = 1.0f; // 标记为消失
                m_VerticesData[grassIndex] = vertex;
                m_DisappearedGrassCount++;
                UpdateGrassBuffer();
            }
        }

        // 让指定区域内的草消失
        public void MakeGrassDisappearInArea(Vector3 center, float radius)
        {
            if (m_GrassSourceMesh == null || m_GrassStates == null) return;

            Vector3[] positions = m_GrassSourceMesh.vertices;
            int disappearedCount = 0;

            for (int i = 0; i < positions.Length && i < m_GrassStates.Count; i++)
            {
                if (!m_GrassStates[i]) // 只处理还存在的草
                {
                    Vector3 worldPos = transform.TransformPoint(positions[i]);
                    float distance = Vector3.Distance(worldPos, center);

                    if (distance <= radius)
                    {
                        m_GrassStates[i] = true;
                        var vertex = m_VerticesData[i];
                        vertex.grassState = 1.0f;
                        m_VerticesData[i] = vertex;
                        disappearedCount++;
                    }
                }
            }

            m_DisappearedGrassCount += disappearedCount;
            UpdateGrassBuffer();
        }

        // 恢复所有草
        public void RestoreAllGrass()
        {
            if (m_GrassStates == null || m_VerticesData == null) return;

            for (int i = 0; i < m_GrassStates.Count && i < m_VerticesData.Count; i++)
            {
                m_GrassStates[i] = false;
                var vertex = m_VerticesData[i];
                vertex.grassState = 0.0f;
                m_VerticesData[i] = vertex;
            }
            m_DisappearedGrassCount = 0;
            UpdateGrassBuffer();
        }

        // 更新GPU缓冲区
        private void UpdateGrassBuffer()
        {
            if (m_DrawBuffer != null && m_VerticesData != null)
            {
                m_DrawBuffer.SetData(m_VerticesData);
            }
        }

        private void Release()
        {
            if (m_Initialized)
            {
                m_DrawBuffer.Release();
                m_ArgsBuffer.Release();
                DestroyImmediate(m_Material);
            }

            m_Initialized = false;
        }

        private void StartDraw()
        {
            m_ArgsBuffer.SetData(m_Args);

            m_Material.SetColor("_TopColor", m_TopColor);
            m_Material.SetColor("_BottomColor", m_BottomColor);

            m_Material.SetColor("_WaveSinColor", m_WaveSinColor);
            m_Material.SetFloat("_WaveA", m_WaveA);
            m_Material.SetFloat("_WaveL", m_WaveL);
            m_Material.SetFloat("_WaveS", m_WaveS);

            Graphics.DrawMeshInstancedIndirect(m_Grass, 0, m_Material, m_LocalBound, m_ArgsBuffer);
        }
    }
}