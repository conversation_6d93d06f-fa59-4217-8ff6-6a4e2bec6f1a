# CS_MowGrass 自定义草地生成功能

## 概述

CS_MowGrass 脚本现在支持两种草地生成模式：
1. **原始模式**：基于 `m_GrassSourceMesh` 的顶点生成草地（保持向后兼容）
2. **自定义模式**：程序化生成草地，支持地形适配和坡度检测

## 新增功能

### 1. 自定义草地生成
- 支持基于地形(Terrain)的草地生成
- 支持基于射线检测的草地生成
- 可控制草地密度和间隔
- 支持坡度角度限制（只在合适的坡度上生成草）

### 2. 地形适配
- 自动适配Unity Terrain组件
- 获取准确的地形高度和法线
- 支持复杂地形的草地生成

### 3. 坡度检测
- 可设置最小和最大坡度角度
- 只在朝上的面（或指定角度范围内）生成草
- 避免在过陡的坡面生成草

## 参数说明

### Grass Generation Settings（草地生成设置）
- **Use Custom Generation**: 是否使用自定义生成模式
- **Terrain**: 目标地形（可选，如果不设置则使用射线检测）
- **Grass Area**: 草地生成区域大小（X, Z轴）
- **Grass Density**: 草地密度倍数（1.0 = 标准密度）
- **Grass Spacing**: 草之间的基础间距
- **Min Slope Angle**: 最小坡度角度（度）
- **Max Slope Angle**: 最大坡度角度（度）
- **Ground Layer Mask**: 地面检测的层级遮罩（射线检测模式）
- **Raycast Height**: 射线检测的起始高度

## 使用方法

### 方法1：使用地形生成
1. 在场景中创建或导入一个Terrain
2. 将CS_MowGrass脚本添加到GameObject上
3. 勾选 "Use Custom Generation"
4. 将Terrain拖拽到 "Terrain" 字段
5. 调整草地区域大小和密度
6. 设置合适的坡度角度范围（例如：0-30度）
7. 点击 "Init" 或运行游戏

### 方法2：使用射线检测生成
1. 确保场景中有带Collider的地面对象
2. 将CS_MowGrass脚本添加到GameObject上
3. 勾选 "Use Custom Generation"
4. 不设置Terrain（保持为null）
5. 设置正确的 "Ground Layer Mask"
6. 调整 "Raycast Height" 确保能检测到地面
7. 设置其他参数并初始化

## 新增方法

### 公共方法
- `RegenerateGrass()`: 重新生成草地
- `GetTotalGrassCount()`: 获取草地总数量
- `GetRemainingGrassCount()`: 获取剩余草的数量
- `SetGrassArea(Vector2 area)`: 动态设置草地区域
- `SetGrassDensity(float density)`: 动态设置草地密度

### 示例代码
```csharp
// 获取草地信息
CS_MowGrass grassScript = GetComponent<CS_MowGrass>();
int totalGrass = grassScript.GetTotalGrassCount();
int remainingGrass = grassScript.GetRemainingGrassCount();

// 动态调整草地
grassScript.SetGrassArea(new Vector2(20f, 20f));
grassScript.SetGrassDensity(1.5f);

// 在指定区域割草
grassScript.MakeGrassDisappearInArea(playerPosition, 2f);

// 恢复所有草地
grassScript.RestoreAllGrass();
```

## 性能优化建议

1. **合理设置密度**：密度过高会影响性能，建议在0.5-2.0之间
2. **限制生成区域**：不要设置过大的草地区域
3. **使用合适的坡度限制**：避免在不必要的地方生成草
4. **批量操作**：尽量避免频繁调用RegenerateGrass()

## 兼容性

- 完全向后兼容原有的基于Mesh的生成方式
- 保留所有原有的割草功能
- 可以在运行时切换生成模式（需要重新初始化）

## 故障排除

### 问题：草地没有生成
- 检查是否勾选了"Use Custom Generation"
- 确认Terrain或Ground Layer Mask设置正确
- 检查坡度角度范围是否合理
- 查看Console是否有错误信息

### 问题：草地生成在错误的位置
- 确认Transform的位置是否正确
- 检查Terrain的位置和大小
- 调整Raycast Height参数

### 问题：性能问题
- 降低草地密度
- 减小草地生成区域
- 检查是否有过多的草地实例
