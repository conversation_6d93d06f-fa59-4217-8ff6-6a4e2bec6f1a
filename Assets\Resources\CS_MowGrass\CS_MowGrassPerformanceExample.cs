using UnityEngine;
using System.Diagnostics;

namespace GameWish.Game
{
    /// <summary>
    /// CS_MowGrass性能测试和空间优化示例脚本
    /// </summary>
    public class CS_MowGrassPerformanceExample : MonoBehaviour
    {
        [Header("References")]
        [SerializeField] private CS_MowGrass m_GrassScript;
        [SerializeField] private Transform m_Player;

        [Header("Performance Test Settings")]
        [SerializeField] private bool m_ShowPerformanceStats = true;
        [SerializeField] private int m_TestIterations = 100;
        [SerializeField] private float m_TestRadius = 2f;
        [SerializeField] private KeyCode m_PerformanceTestKey = KeyCode.P;
        [SerializeField] private KeyCode m_ToggleOptimizationKey = KeyCode.O;
        [SerializeField] private KeyCode m_ShowStatsKey = KeyCode.I;

        [Header("Mowing Settings")]
        [SerializeField] private float m_MowRadius = 2f;
        [SerializeField] private KeyCode m_MowKey = KeyCode.Space;
        [SerializeField] private KeyCode m_RestoreKey = KeyCode.R;

        private bool m_IsOptimizationEnabled = true;

        private void Start()
        {
            if (m_GrassScript == null)
            {
                m_GrassScript = GetComponent<CS_MowGrass>();
            }

            // 显示初始信息
            LogGrassInfo();
            
            if (m_ShowPerformanceStats)
            {
                UnityEngine.Debug.Log(m_GrassScript.GetSpatialGridStats());
            }
        }

        private void Update()
        {
            HandleInput();
        }

        private void HandleInput()
        {
            // 基本割草功能
            if (Input.GetKeyDown(m_MowKey))
            {
                MowGrassAroundPlayer();
            }

            if (Input.GetKeyDown(m_RestoreKey))
            {
                RestoreAllGrass();
            }

            // 性能测试功能
            if (Input.GetKeyDown(m_PerformanceTestKey))
            {
                RunPerformanceTest();
            }

            if (Input.GetKeyDown(m_ToggleOptimizationKey))
            {
                ToggleSpatialOptimization();
            }

            if (Input.GetKeyDown(m_ShowStatsKey))
            {
                ShowSpatialGridStats();
            }
        }

        /// <summary>
        /// 在玩家周围割草
        /// </summary>
        private void MowGrassAroundPlayer()
        {
            if (m_Player != null && m_GrassScript != null)
            {
                Vector3 playerPos = m_Player.position;
                
                Stopwatch sw = Stopwatch.StartNew();
                m_GrassScript.MakeGrassDisappearInArea(playerPos, m_MowRadius);
                sw.Stop();
                
                UnityEngine.Debug.Log($"Mowed grass around player in {sw.ElapsedMilliseconds}ms. Remaining: {m_GrassScript.GetRemainingGrassCount()}");
            }
        }

        /// <summary>
        /// 恢复所有草地
        /// </summary>
        private void RestoreAllGrass()
        {
            if (m_GrassScript != null)
            {
                m_GrassScript.RestoreAllGrass();
                UnityEngine.Debug.Log("All grass restored!");
                LogGrassInfo();
            }
        }

        /// <summary>
        /// 运行性能测试
        /// </summary>
        private void RunPerformanceTest()
        {
            if (m_GrassScript == null || m_Player == null)
            {
                UnityEngine.Debug.LogWarning("Grass script or player not assigned!");
                return;
            }

            UnityEngine.Debug.Log($"Starting performance test with {m_TestIterations} iterations...");
            
            Vector3 testCenter = m_Player.position;
            
            // 测试当前优化状态
            Stopwatch sw = Stopwatch.StartNew();
            
            for (int i = 0; i < m_TestIterations; i++)
            {
                // 随机测试位置
                Vector3 randomOffset = new Vector3(
                    Random.Range(-5f, 5f),
                    0,
                    Random.Range(-5f, 5f)
                );
                Vector3 testPos = testCenter + randomOffset;
                
                m_GrassScript.MakeGrassDisappearInArea(testPos, m_TestRadius);
            }
            
            sw.Stop();
            
            string optimizationStatus = m_IsOptimizationEnabled ? "WITH spatial optimization" : "WITHOUT spatial optimization";
            UnityEngine.Debug.Log($"Performance test completed {optimizationStatus}:");
            UnityEngine.Debug.Log($"- {m_TestIterations} iterations in {sw.ElapsedMilliseconds}ms");
            UnityEngine.Debug.Log($"- Average: {(float)sw.ElapsedMilliseconds / m_TestIterations:F2}ms per operation");
            UnityEngine.Debug.Log($"- Remaining grass: {m_GrassScript.GetRemainingGrassCount()}");
            
            // 恢复草地以便下次测试
            m_GrassScript.RestoreAllGrass();
        }

        /// <summary>
        /// 切换空间优化
        /// </summary>
        private void ToggleSpatialOptimization()
        {
            if (m_GrassScript != null)
            {
                m_IsOptimizationEnabled = !m_IsOptimizationEnabled;
                m_GrassScript.SetUseSpatialOptimization(m_IsOptimizationEnabled);
                
                string status = m_IsOptimizationEnabled ? "ENABLED" : "DISABLED";
                UnityEngine.Debug.Log($"Spatial optimization {status}");
                
                if (m_IsOptimizationEnabled)
                {
                    UnityEngine.Debug.Log(m_GrassScript.GetSpatialGridStats());
                }
            }
        }

        /// <summary>
        /// 显示空间网格统计信息
        /// </summary>
        private void ShowSpatialGridStats()
        {
            if (m_GrassScript != null)
            {
                UnityEngine.Debug.Log(m_GrassScript.GetSpatialGridStats());
            }
        }

        /// <summary>
        /// 输出草地信息到控制台
        /// </summary>
        private void LogGrassInfo()
        {
            if (m_GrassScript != null)
            {
                int total = m_GrassScript.GetTotalGrassCount();
                int remaining = m_GrassScript.GetRemainingGrassCount();
                int disappeared = m_GrassScript.GetDisappearedGrassCount();

                UnityEngine.Debug.Log($"Grass Info - Total: {total}, Remaining: {remaining}, Disappeared: {disappeared}");
            }
        }

        /// <summary>
        /// 在Scene视图中显示测试范围
        /// </summary>
        private void OnDrawGizmosSelected()
        {
            if (m_Player != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(m_Player.position, m_MowRadius);
                
                Gizmos.color = Color.blue;
                Gizmos.DrawWireSphere(m_Player.position, m_TestRadius);
            }
        }

        /// <summary>
        /// 显示使用说明
        /// </summary>
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 400, 300));
            GUILayout.Label("CS_MowGrass Performance Test:", GUI.skin.box);
            
            GUILayout.Label($"Press {m_MowKey} - Mow grass around player");
            GUILayout.Label($"Press {m_RestoreKey} - Restore all grass");
            GUILayout.Label($"Press {m_PerformanceTestKey} - Run performance test");
            GUILayout.Label($"Press {m_ToggleOptimizationKey} - Toggle spatial optimization");
            GUILayout.Label($"Press {m_ShowStatsKey} - Show spatial grid stats");

            if (m_GrassScript != null)
            {
                GUILayout.Space(10);
                GUILayout.Label($"Total Grass: {m_GrassScript.GetTotalGrassCount()}");
                GUILayout.Label($"Remaining: {m_GrassScript.GetRemainingGrassCount()}");
                GUILayout.Label($"Disappeared: {m_GrassScript.GetDisappearedGrassCount()}");
                
                string optimizationStatus = m_IsOptimizationEnabled ? "ENABLED" : "DISABLED";
                GUILayout.Label($"Spatial Optimization: {optimizationStatus}");
            }
            
            GUILayout.EndArea();
        }
    }
}
