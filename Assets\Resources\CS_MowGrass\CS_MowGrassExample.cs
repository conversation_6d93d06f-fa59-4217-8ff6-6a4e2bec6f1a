using UnityEngine;

namespace GameWish.Game
{
    /// <summary>
    /// CS_MowGrass使用示例脚本
    /// 演示如何使用新的自定义草地生成功能
    /// </summary>
    public class CS_MowGrassExample : MonoBehaviour
    {
        [Header("References")]
        [SerializeField] private CS_MowGrass m_GrassScript;
        [SerializeField] private Transform m_Player;

        [Header("Mowing Settings")]
        [SerializeField] private float m_MowRadius = 2f;
        [SerializeField] private KeyCode m_MowKey = KeyCode.Space;
        [SerializeField] private KeyCode m_RestoreKey = KeyCode.R;

        [Header("Dynamic Settings")]
        [SerializeField] private bool m_AllowDynamicChanges = true;
        [SerializeField] private Vector2 m_NewGrassArea = new Vector2(20f, 20f);
        [SerializeField] private float m_NewGrassDensity = 1.5f;

        [Header("Custom Mesh Test")]
        [SerializeField] private Mesh m_TestCustomMesh;
        [SerializeField] private Transform m_TestMeshTransform;

        private void Start()
        {
            if (m_GrassScript == null)
            {
                m_GrassScript = GetComponent<CS_MowGrass>();
            }

            // 显示初始草地信息
            LogGrassInfo();
        }

        private void Update()
        {
            HandleInput();
        }

        private void HandleInput()
        {
            // 割草
            if (Input.GetKeyDown(m_MowKey))
            {
                MowGrassAroundPlayer();
            }

            // 恢复草地
            if (Input.GetKeyDown(m_RestoreKey))
            {
                RestoreAllGrass();
            }

            // 动态调整草地设置（仅在允许时）
            if (m_AllowDynamicChanges)
            {
                if (Input.GetKeyDown(KeyCode.Alpha1))
                {
                    ChangeGrassArea();
                }

                if (Input.GetKeyDown(KeyCode.Alpha2))
                {
                    ChangeGrassDensity();
                }

                if (Input.GetKeyDown(KeyCode.Alpha3))
                {
                    TestCustomMesh();
                }
            }
        }

        /// <summary>
        /// 在玩家周围割草
        /// </summary>
        private void MowGrassAroundPlayer()
        {
            if (m_Player != null && m_GrassScript != null)
            {
                Vector3 playerPos = m_Player.position;
                m_GrassScript.MakeGrassDisappearInArea(playerPos, m_MowRadius);

                Debug.Log($"Mowed grass around player. Remaining grass: {m_GrassScript.GetRemainingGrassCount()}");
            }
        }

        /// <summary>
        /// 恢复所有草地
        /// </summary>
        private void RestoreAllGrass()
        {
            if (m_GrassScript != null)
            {
                m_GrassScript.RestoreAllGrass();
                Debug.Log("All grass restored!");
                LogGrassInfo();
            }
        }

        /// <summary>
        /// 改变草地区域大小
        /// </summary>
        private void ChangeGrassArea()
        {
            if (m_GrassScript != null)
            {
                m_GrassScript.SetGrassArea(m_NewGrassArea);
                Debug.Log($"Changed grass area to: {m_NewGrassArea}");
                LogGrassInfo();
            }
        }

        /// <summary>
        /// 改变草地密度
        /// </summary>
        private void ChangeGrassDensity()
        {
            if (m_GrassScript != null)
            {
                m_GrassScript.SetGrassDensity(m_NewGrassDensity);
                Debug.Log($"Changed grass density to: {m_NewGrassDensity}");
                LogGrassInfo();
            }
        }

        /// <summary>
        /// 测试自定义Mesh生成
        /// </summary>
        private void TestCustomMesh()
        {
            if (m_GrassScript != null && m_TestCustomMesh != null)
            {
                // 使用新的公共方法设置自定义Mesh
                m_GrassScript.SetGenerationMode(CS_MowGrass.GenerationMode.CustomMesh);
                m_GrassScript.SetCustomMesh(m_TestCustomMesh, m_TestMeshTransform);

                Debug.Log($"Switched to custom mesh generation using: {m_TestCustomMesh.name}");
                LogGrassInfo();
            }
            else
            {
                Debug.LogWarning("Test custom mesh or grass script is not assigned!");
            }
        }

        /// <summary>
        /// 输出草地信息到控制台
        /// </summary>
        private void LogGrassInfo()
        {
            if (m_GrassScript != null)
            {
                int total = m_GrassScript.GetTotalGrassCount();
                int remaining = m_GrassScript.GetRemainingGrassCount();
                int disappeared = m_GrassScript.GetDisappearedGrassCount();

                Debug.Log($"Grass Info - Total: {total}, Remaining: {remaining}, Disappeared: {disappeared}");
            }
        }

        /// <summary>
        /// 在Scene视图中显示割草范围
        /// </summary>
        private void OnDrawGizmosSelected()
        {
            if (m_Player != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(m_Player.position, m_MowRadius);
            }
        }

        /// <summary>
        /// 显示使用说明
        /// </summary>
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("CS_MowGrass Controls:", GUI.skin.box);
            GUILayout.Label($"Press {m_MowKey} - Mow grass around player");
            GUILayout.Label($"Press {m_RestoreKey} - Restore all grass");

            if (m_AllowDynamicChanges)
            {
                GUILayout.Label("Press 1 - Change grass area");
                GUILayout.Label("Press 2 - Change grass density");
                GUILayout.Label("Press 3 - Test custom mesh");
            }

            if (m_GrassScript != null)
            {
                GUILayout.Space(10);
                GUILayout.Label($"Total Grass: {m_GrassScript.GetTotalGrassCount()}");
                GUILayout.Label($"Remaining: {m_GrassScript.GetRemainingGrassCount()}");
                GUILayout.Label($"Disappeared: {m_GrassScript.GetDisappearedGrassCount()}");
            }
            GUILayout.EndArea();
        }
    }
}
