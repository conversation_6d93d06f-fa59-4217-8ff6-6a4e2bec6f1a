using UnityEngine;

namespace GameWish.Game
{
    /// <summary>
    /// CS_MowGrass测试场景设置脚本
    /// 帮助快速创建和测试不同的草地生成模式
    /// </summary>
    public class CS_MowGrassTestSetup : MonoBehaviour
    {
        [Header("Test Objects")]
        [SerializeField] private CS_MowGrass m_GrassScript;
        [SerializeField] private GameObject m_TestPlanePrefab;
        [SerializeField] private Terrain m_TestTerrain;

        [Header("Test Settings")]
        [SerializeField] private Vector2 m_TestArea = new Vector2(10f, 10f);
        [SerializeField] private float m_TestDensity = 1f;

        private GameObject m_CurrentTestPlane;

        private void Start()
        {
            if (m_GrassScript == null)
            {
                m_GrassScript = FindObjectOfType<CS_MowGrass>();
            }
        }

        /// <summary>
        /// 创建测试平面并设置为自定义Mesh模式
        /// </summary>
        [ContextMenu("Test Custom Mesh - Plane")]
        public void TestCustomMeshPlane()
        {
            // 清理之前的测试对象
            CleanupTestObjects();

            // 创建测试平面
            m_CurrentTestPlane = GameObject.CreatePrimitive(PrimitiveType.Plane);
            m_CurrentTestPlane.name = "Test Grass Plane";
            m_CurrentTestPlane.transform.position = transform.position;
            m_CurrentTestPlane.transform.localScale = new Vector3(2f, 1f, 2f);

            // 设置草地脚本
            if (m_GrassScript != null)
            {
                m_GrassScript.SetGenerationMode(CS_MowGrass.GenerationMode.CustomMesh);
                m_GrassScript.SetCustomMesh(m_CurrentTestPlane.GetComponent<MeshFilter>().sharedMesh, 
                                          m_CurrentTestPlane.transform);
                m_GrassScript.SetGrassArea(m_TestArea);
                m_GrassScript.SetGrassDensity(m_TestDensity);
                m_GrassScript.RegenerateGrass();

                Debug.Log("Created test plane for custom mesh grass generation");
            }
        }

        /// <summary>
        /// 创建测试球体并设置为自定义Mesh模式
        /// </summary>
        [ContextMenu("Test Custom Mesh - Sphere")]
        public void TestCustomMeshSphere()
        {
            // 清理之前的测试对象
            CleanupTestObjects();

            // 创建测试球体
            m_CurrentTestPlane = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            m_CurrentTestPlane.name = "Test Grass Sphere";
            m_CurrentTestPlane.transform.position = transform.position;
            m_CurrentTestPlane.transform.localScale = Vector3.one * 3f;

            // 设置草地脚本
            if (m_GrassScript != null)
            {
                m_GrassScript.SetGenerationMode(CS_MowGrass.GenerationMode.CustomMesh);
                m_GrassScript.SetCustomMesh(m_CurrentTestPlane.GetComponent<MeshFilter>().sharedMesh, 
                                          m_CurrentTestPlane.transform);
                m_GrassScript.SetGrassArea(m_TestArea);
                m_GrassScript.SetGrassDensity(m_TestDensity);
                m_GrassScript.SetSlopeAngleRange(0f, 60f); // 允许在球体表面生成
                m_GrassScript.RegenerateGrass();

                Debug.Log("Created test sphere for custom mesh grass generation");
            }
        }

        /// <summary>
        /// 测试地形模式
        /// </summary>
        [ContextMenu("Test Terrain Mode")]
        public void TestTerrainMode()
        {
            if (m_TestTerrain != null && m_GrassScript != null)
            {
                m_GrassScript.SetGenerationMode(CS_MowGrass.GenerationMode.Terrain);
                m_GrassScript.SetTerrain(m_TestTerrain);
                m_GrassScript.SetGrassArea(m_TestArea);
                m_GrassScript.SetGrassDensity(m_TestDensity);
                m_GrassScript.SetSlopeAngleRange(0f, 30f);
                m_GrassScript.RegenerateGrass();

                Debug.Log("Switched to terrain mode for grass generation");
            }
            else
            {
                Debug.LogWarning("Test terrain or grass script is not assigned!");
            }
        }

        /// <summary>
        /// 测试射线检测模式
        /// </summary>
        [ContextMenu("Test Raycast Mode")]
        public void TestRaycastMode()
        {
            // 清理之前的测试对象
            CleanupTestObjects();

            // 创建地面对象
            m_CurrentTestPlane = GameObject.CreatePrimitive(PrimitiveType.Plane);
            m_CurrentTestPlane.name = "Test Ground Plane";
            m_CurrentTestPlane.transform.position = transform.position + Vector3.down * 2f;
            m_CurrentTestPlane.transform.localScale = new Vector3(3f, 1f, 3f);

            // 设置草地脚本
            if (m_GrassScript != null)
            {
                m_GrassScript.SetGenerationMode(CS_MowGrass.GenerationMode.Raycast);
                m_GrassScript.SetGrassArea(m_TestArea);
                m_GrassScript.SetGrassDensity(m_TestDensity);
                m_GrassScript.RegenerateGrass();

                Debug.Log("Created test ground plane for raycast grass generation");
            }
        }

        /// <summary>
        /// 清理测试对象
        /// </summary>
        [ContextMenu("Cleanup Test Objects")]
        public void CleanupTestObjects()
        {
            if (m_CurrentTestPlane != null)
            {
                if (Application.isPlaying)
                {
                    Destroy(m_CurrentTestPlane);
                }
                else
                {
                    DestroyImmediate(m_CurrentTestPlane);
                }
                m_CurrentTestPlane = null;
            }
        }

        /// <summary>
        /// 显示当前草地信息
        /// </summary>
        [ContextMenu("Show Grass Info")]
        public void ShowGrassInfo()
        {
            if (m_GrassScript != null)
            {
                int total = m_GrassScript.GetTotalGrassCount();
                int remaining = m_GrassScript.GetRemainingGrassCount();
                int disappeared = m_GrassScript.GetDisappearedGrassCount();

                Debug.Log($"=== Grass Information ===");
                Debug.Log($"Total Grass: {total}");
                Debug.Log($"Remaining Grass: {remaining}");
                Debug.Log($"Disappeared Grass: {disappeared}");
                Debug.Log($"=========================");
            }
        }

        private void OnDestroy()
        {
            CleanupTestObjects();
        }

        private void OnDrawGizmosSelected()
        {
            // 显示测试区域
            Gizmos.color = Color.magenta;
            Gizmos.DrawWireCube(transform.position, new Vector3(m_TestArea.x, 1f, m_TestArea.y));
        }
    }
}
